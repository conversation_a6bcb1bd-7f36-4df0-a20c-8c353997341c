'use client';
import { useState, useEffect } from 'react';
import styles from './ConsumerPulseTab.module.css';

export default function ConsumerPulseTab() {
  const [articles, setArticles] = useState([]);
  const [surveys, setSurveys] = useState([]);
  const [polls, setPolls] = useState([]);
  const [loading, setLoading] = useState(true);
  const [scraping, setScraping] = useState(false);
  const [autoScraping, setAutoScraping] = useState(false);

  useEffect(() => {
    fetchConsumerPulseData();
  }, []);

  const fetchConsumerPulseData = async () => {
    try {
      setLoading(true);
      
      const [articlesRes, surveysRes, pollsRes] = await Promise.all([
        fetch('/api/consumer-pulse/articles?limit=20'),
        fetch('/api/consumer-pulse/surveys'),
        fetch('/api/consumer-pulse/polls')
      ]);

      const [articlesData, surveysData, pollsData] = await Promise.all([
        articlesRes.json(),
        surveysRes.json(),
        pollsRes.json()
      ]);

      if (articlesData.success) setArticles(articlesData.articles);
      if (surveysData.success) setSurveys(surveysData.surveys);
      if (pollsData.success) setPolls(pollsData.polls);
      
    } catch (error) {
      console.error('Error fetching Consumer Pulse data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleManualScraping = async () => {
    try {
      setScraping(true);
      const response = await fetch('/api/consumer-pulse/scrape-news', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const data = await response.json();
      
      if (data.success) {
        alert(`News scraping completed! Scraped: ${data.scraped}, Generated: ${data.generated}, Saved: ${data.saved}`);
        fetchConsumerPulseData(); // Refresh data
      } else {
        alert('Error during news scraping: ' + data.details);
      }
    } catch (error) {
      console.error('Error triggering news scraping:', error);
      alert('Error triggering news scraping');
    } finally {
      setScraping(false);
    }
  };

  const handleToggleAutoScraping = async () => {
    try {
      const response = await fetch('/api/consumer-pulse/start-auto-scraping', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ intervalHours: 4 })
      });

      const data = await response.json();
      
      if (data.success) {
        setAutoScraping(true);
        alert('Automatic news scraping started (4-hour intervals)');
      } else {
        alert('Error starting auto scraping: ' + data.details);
      }
    } catch (error) {
      console.error('Error toggling auto scraping:', error);
      alert('Error toggling auto scraping');
    }
  };

  const handlePublishArticle = async (articleId) => {
    try {
      const response = await fetch(`/api/consumer-pulse/articles/${articleId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ status: 'PUBLISHED' })
      });

      const data = await response.json();
      
      if (data.success) {
        fetchConsumerPulseData(); // Refresh data
      } else {
        alert('Error publishing article: ' + data.details);
      }
    } catch (error) {
      console.error('Error publishing article:', error);
      alert('Error publishing article');
    }
  };

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loadingSpinner}></div>
        <p>Loading Consumer Pulse data...</p>
      </div>
    );
  }

  return (
    <div className={styles.consumerPulseTab}>
      <div className={styles.header}>
        <h2>Consumer Pulse Management</h2>
        <div className={styles.controls}>
          <button
            onClick={handleManualScraping}
            disabled={scraping}
            className={styles.scrapeButton}
          >
            {scraping ? 'Scraping...' : 'Manual News Scraping'}
          </button>
          <button
            onClick={handleToggleAutoScraping}
            disabled={autoScraping}
            className={styles.autoButton}
          >
            {autoScraping ? 'Auto Scraping Active' : 'Start Auto Scraping'}
          </button>
        </div>
      </div>

      <div className={styles.statsGrid}>
        <div className={styles.statCard}>
          <h3>Articles</h3>
          <div className={styles.statNumber}>{articles.length}</div>
          <div className={styles.statLabel}>Total Generated</div>
        </div>
        <div className={styles.statCard}>
          <h3>Surveys</h3>
          <div className={styles.statNumber}>{surveys.length}</div>
          <div className={styles.statLabel}>Active Surveys</div>
        </div>
        <div className={styles.statCard}>
          <h3>Polls</h3>
          <div className={styles.statNumber}>{polls.length}</div>
          <div className={styles.statLabel}>Live Polls</div>
        </div>
        <div className={styles.statCard}>
          <h3>Published</h3>
          <div className={styles.statNumber}>
            {articles.filter(a => a.status === 'PUBLISHED').length}
          </div>
          <div className={styles.statLabel}>Published Articles</div>
        </div>
      </div>

      <div className={styles.articlesSection}>
        <h3>Recent Articles</h3>
        <div className={styles.articlesList}>
          {articles.slice(0, 10).map(article => (
            <div key={article.id} className={styles.articleCard}>
              <div className={styles.articleInfo}>
                <h4 className={styles.articleTitle}>{article.title}</h4>
                <p className={styles.articleSummary}>
                  {article.summary || article.content?.substring(0, 100) + '...'}
                </p>
                <div className={styles.articleMeta}>
                  <span className={`${styles.status} ${styles[article.status?.toLowerCase()]}`}>
                    {article.status}
                  </span>
                  {article.sentiment && (
                    <span className={`${styles.sentiment} ${styles[article.sentiment?.toLowerCase()]}`}>
                      {article.sentiment}
                    </span>
                  )}
                  <span className={styles.date}>
                    {new Date(article.createdAt).toLocaleDateString()}
                  </span>
                </div>
              </div>
              <div className={styles.articleActions}>
                {article.status === 'DRAFT' && (
                  <button
                    onClick={() => handlePublishArticle(article.id)}
                    className={styles.publishButton}
                  >
                    Publish
                  </button>
                )}
                <button className={styles.viewButton}>
                  View
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
