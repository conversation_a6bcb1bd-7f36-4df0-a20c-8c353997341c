.consumerPulseTab {
  padding: 2rem;
  background: #f8f9fa;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #dee2e6;
}

.header h2 {
  color: #2c3e50;
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
}

.controls {
  display: flex;
  gap: 1rem;
}

.scrapeButton, .autoButton {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.scrapeButton {
  background: #007bff;
  color: white;
}

.scrapeButton:hover:not(:disabled) {
  background: #0056b3;
}

.scrapeButton:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.autoButton {
  background: #28a745;
  color: white;
}

.autoButton:hover:not(:disabled) {
  background: #1e7e34;
}

.autoButton:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.statCard {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
}

.statCard h3 {
  margin: 0 0 1rem 0;
  color: #495057;
  font-size: 1rem;
  font-weight: 500;
}

.statNumber {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.statLabel {
  color: #6c757d;
  font-size: 0.9rem;
}

.articlesSection {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.articlesSection h3 {
  margin: 0 0 1.5rem 0;
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
}

.articlesList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.articleCard {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1rem;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  background: #f8f9fa;
}

.articleInfo {
  flex: 1;
  margin-right: 1rem;
}

.articleTitle {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
}

.articleSummary {
  margin: 0 0 0.75rem 0;
  color: #495057;
  font-size: 0.9rem;
  line-height: 1.4;
}

.articleMeta {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.status {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.status.draft {
  background: #ffc107;
  color: #212529;
}

.status.published {
  background: #28a745;
  color: white;
}

.sentiment {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.sentiment.positive {
  background: #d4edda;
  color: #155724;
}

.sentiment.negative {
  background: #f8d7da;
  color: #721c24;
}

.sentiment.neutral {
  background: #e2e3e5;
  color: #383d41;
}

.date {
  color: #6c757d;
  font-size: 0.8rem;
}

.articleActions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.publishButton, .viewButton {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.publishButton {
  background: #28a745;
  color: white;
}

.publishButton:hover {
  background: #1e7e34;
}

.viewButton {
  background: #6c757d;
  color: white;
}

.viewButton:hover {
  background: #545b62;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: #6c757d;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .consumerPulseTab {
    padding: 1rem;
  }
  
  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .controls {
    justify-content: center;
  }
  
  .articleCard {
    flex-direction: column;
    gap: 1rem;
  }
  
  .articleInfo {
    margin-right: 0;
  }
  
  .articleActions {
    flex-direction: row;
    justify-content: flex-end;
  }
}
