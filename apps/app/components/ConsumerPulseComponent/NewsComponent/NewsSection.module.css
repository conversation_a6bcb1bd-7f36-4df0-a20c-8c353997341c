.newsSection {
    padding: 4rem 2rem;
    background: #fff;
    border-top: 1px solid #e0e0e0;
  }
  
  .sectionHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
  }

  .titleSection {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .lastUpdated {
    font-size: 0.8rem;
    color: #666;
    font-style: italic;
  }

  .sectionTitle {
    font-size: 1.8rem;
    font-weight: 700;
    font-family: 'Helvetica Neue', sans-serif;
    color: #111;
    margin: 0;
  }

  .refreshButton {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #495057;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s ease;
  }

  .refreshButton:hover:not(:disabled) {
    background: #e9ecef;
    border-color: #adb5bd;
  }

  .refreshButton:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  .newsGrid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
  }
  
  .newsCard {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    border-bottom: 1px solid #ddd;
    padding-bottom: 1.5rem;
  }
  
  .image {
    width: 100%;
    height: auto;
    border-radius: 4px;
    object-fit: cover;
  }
  
  .title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #000;
    font-family: 'Helvetica Neue', sans-serif;
  }
  
  .desc {
    font-size: 0.95rem;
    color: #444;
    line-height: 1.5;
  }

  .loadingGrid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
  }

  .loadingCard {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    border-bottom: 1px solid #ddd;
    padding-bottom: 1.5rem;
  }

  .loadingImage {
    width: 100%;
    height: 200px;
    background: #f0f0f0;
    border-radius: 4px;
    animation: pulse 1.5s ease-in-out infinite;
  }

  .loadingText {
    height: 20px;
    background: #f0f0f0;
    border-radius: 4px;
    animation: pulse 1.5s ease-in-out infinite;
    animation-delay: 0.2s;
  }

  .loadingDesc {
    height: 16px;
    background: #f0f0f0;
    border-radius: 4px;
    width: 80%;
    animation: pulse 1.5s ease-in-out infinite;
    animation-delay: 0.4s;
  }

  .metadata {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
    align-items: center;
  }

  .category {
    background: #f0f0f0;
    color: #333;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .sentiment {
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .sentiment[data-sentiment="POSITIVE"] {
    background: #e8f5e8;
    color: #2e7d32;
  }

  .sentiment[data-sentiment="NEGATIVE"] {
    background: #ffebee;
    color: #c62828;
  }

  .sentiment[data-sentiment="NEUTRAL"] {
    background: #f5f5f5;
    color: #666;
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }
  