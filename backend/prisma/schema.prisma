// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums
enum ServiceType {
  FRONTEND_WEB_DESIGN
  SAVOUR_AND_SIP
  CONSUMER_PULSE
}

enum ProjectStatus {
  INQUIRY
  QUOTED
  APPROVED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum InvoiceStatus {
  DRAFT
  SENT
  PAID
  OVERDUE
  CANCELLED
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}

// Core Models
model Client {
  id          String   @id @default(cuid())
  email       String   @unique
  firstName   String
  lastName    String
  phone       String?
  company     String?
  address     String?
  city        String?
  province    String?
  postalCode  String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  projects    Project[]
  invoices    Invoice[]
  bookings    Booking[]
  quotes      Quote[]
  apiKeys     APIKey[]

  @@map("clients")
}

model Project {
  id            String        @id @default(cuid())
  clientId      String
  serviceType   ServiceType
  title         String
  description   String?
  status        ProjectStatus @default(INQUIRY)
  estimatedCost Float?
  finalCost     Float?
  startDate     DateTime?
  endDate       DateTime?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Frontend Web Design specific fields
  websiteUrl    String?
  domainName    String?
  hostingPlan   String?

  // Savour & Sip specific fields
  eventDate     DateTime?
  eventLocation String?
  guestCount    Int?
  eventType     String?

  // Relationships
  client        Client        @relation(fields: [clientId], references: [id], onDelete: Cascade)
  invoices      Invoice[]
  projectFiles  ProjectFile[]
  tasks         Task[]

  @@map("projects")
}

model Invoice {
  id            String        @id @default(cuid())
  clientId      String
  projectId     String?
  invoiceNumber String        @unique
  title         String
  description   String?
  subtotal      Float
  tax           Float         @default(0)
  total         Float
  status        InvoiceStatus @default(DRAFT)
  issueDate     DateTime      @default(now())
  dueDate       DateTime
  paidDate      DateTime?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relationships
  client        Client        @relation(fields: [clientId], references: [id], onDelete: Cascade)
  project       Project?      @relation(fields: [projectId], references: [id], onDelete: SetNull)
  invoiceItems  InvoiceItem[]
  payments      Payment[]

  @@map("invoices")
}

model InvoiceItem {
  id          String  @id @default(cuid())
  invoiceId   String
  description String
  quantity    Int     @default(1)
  unitPrice   Float
  total       Float

  // Relationships
  invoice     Invoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade)

  @@map("invoice_items")
}

model Payment {
  id            String        @id @default(cuid())
  invoiceId     String
  amount        Float
  status        PaymentStatus @default(PENDING)
  paymentMethod String?
  transactionId String?
  paidAt        DateTime?
  createdAt     DateTime      @default(now())

  // Relationships
  invoice       Invoice       @relation(fields: [invoiceId], references: [id], onDelete: Cascade)

  @@map("payments")
}

model Booking {
  id            String      @id @default(cuid())
  clientId      String
  serviceType   ServiceType
  eventDate     DateTime
  eventLocation String
  guestCount    Int?
  services      String      // JSON array of requested services
  specialRequests String?
  status        String      @default("pending")
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  // Relationships
  client        Client      @relation(fields: [clientId], references: [id], onDelete: Cascade)

  @@map("bookings")
}

model Task {
  id          String   @id @default(cuid())
  projectId   String
  title       String
  description String?
  status      String   @default("pending") // pending, in_progress, completed
  priority    String   @default("medium") // low, medium, high
  dueDate     DateTime?
  completedAt DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  project     Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@map("tasks")
}

model ProjectFile {
  id          String   @id @default(cuid())
  projectId   String
  fileName    String
  originalName String
  filePath    String
  fileSize    Int
  mimeType    String
  uploadedAt  DateTime @default(now())

  // Relationships
  project     Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@map("project_files")
}

model Admin {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  firstName String
  lastName  String
  role      String   @default("admin")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("admins")
}

model Quote {
  id            String      @id @default(cuid())
  clientId      String?
  serviceType   ServiceType
  status        QuoteStatus @default(PENDING)

  // Common fields
  name          String
  email         String
  phone         String?
  message       String?

  // Savour & Sip specific fields
  eventType     String?
  guestCount    Int?
  eventDate     DateTime?
  services      String?     // JSON array of requested services

  // Frontend Web Design specific fields
  company       String?
  websiteType   String?
  budget        String?

  // Admin response
  quotedAmount  Float?
  adminNotes    String?
  quotedAt      DateTime?
  quotedBy      String?     // Admin ID who provided the quote

  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  // Relationships
  client        Client?     @relation(fields: [clientId], references: [id], onDelete: SetNull)

  @@map("quotes")
}

enum QuoteStatus {
  PENDING
  QUOTED
  ACCEPTED
  REJECTED
  CONVERTED_TO_PROJECT
}

// Consumer Pulse Enums
enum SurveyStatus {
  DRAFT
  ACTIVE
  PAUSED
  COMPLETED
  ARCHIVED
}

enum PollStatus {
  ACTIVE
  CLOSED
  ARCHIVED
}

enum ArticleStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

enum SentimentType {
  POSITIVE
  NEGATIVE
  NEUTRAL
  MIXED
}

enum APIAccessTier {
  FREE
  BASIC
  PREMIUM
  ENTERPRISE
}

// Consumer Pulse Models
model Survey {
  id            String        @id @default(cuid())
  title         String
  description   String?
  status        SurveyStatus  @default(DRAFT)
  targetAudience String?
  questions     String        // JSON array of questions
  settings      String?       // JSON object for survey settings
  createdBy     String        // Admin ID
  startDate     DateTime?
  endDate       DateTime?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relationships
  responses     SurveyResponse[]
  analytics     SurveyAnalytics[]

  @@map("surveys")
}

model SurveyResponse {
  id          String   @id @default(cuid())
  surveyId    String
  respondentId String? // Optional user ID if registered
  responses   String   // JSON object with question IDs and answers
  ipAddress   String?
  userAgent   String?
  location    String?  // Geolocation data
  completedAt DateTime @default(now())

  // AI Sentiment Analysis Fields
  sentiment          String? // POSITIVE, NEGATIVE, NEUTRAL
  sentimentScore     Float?  // -1 to 1 sentiment score
  sentimentConfidence Float? // 0 to 1 confidence level
  emotions           String? // JSON array of detected emotions
  themes             String? // JSON array of detected themes
  demographics       String? // JSON object with demographic data

  // Relationships
  survey      Survey   @relation(fields: [surveyId], references: [id], onDelete: Cascade)

  @@map("survey_responses")
}

model Poll {
  id          String     @id @default(cuid())
  title       String
  question    String
  options     String     // JSON array of poll options
  status      PollStatus @default(ACTIVE)
  allowMultiple Boolean  @default(false)
  endDate     DateTime?
  createdBy   String     // Admin ID
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // Relationships
  votes       PollVote[]

  @@map("polls")
}

model PollVote {
  id          String   @id @default(cuid())
  pollId      String
  optionIndex Int      // Index of selected option
  voterId     String?  // Optional user ID if registered
  ipAddress   String?
  userAgent   String?
  votedAt     DateTime @default(now())

  // Relationships
  poll        Poll     @relation(fields: [pollId], references: [id], onDelete: Cascade)

  @@unique([pollId, ipAddress]) // Prevent duplicate votes from same IP
  @@map("poll_votes")
}

model NewsArticle {
  id            String        @id @default(cuid())
  title         String
  content       String
  summary       String?
  author        String?
  sourceUrl     String?
  imageUrl      String?
  keywords      String        // JSON array of keywords
  category      String?
  status        ArticleStatus @default(DRAFT)
  sentiment     SentimentType?
  sentimentScore Float?       // -1 to 1 scale
  viewCount     Int           @default(0)
  shareCount    Int           @default(0)
  publishedAt   DateTime?
  scrapedAt     DateTime?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relationships
  analytics     ArticleAnalytics[]

  @@map("news_articles")
}

model SurveyAnalytics {
  id              String   @id @default(cuid())
  surveyId        String
  totalResponses  Int      @default(0)
  completionRate  Float?   // Percentage
  averageTime     Int?     // Seconds
  sentimentScore  Float?   // Overall sentiment
  insights        String?  // JSON object with AI-generated insights
  demographics    String?  // JSON object with demographic breakdown
  generatedAt     DateTime @default(now())

  // Relationships
  survey          Survey   @relation(fields: [surveyId], references: [id], onDelete: Cascade)

  @@map("survey_analytics")
}

model ArticleAnalytics {
  id              String      @id @default(cuid())
  articleId       String
  views           Int         @default(0)
  shares          Int         @default(0)
  engagementRate  Float?      // Percentage
  readTime        Int?        // Average read time in seconds
  bounceRate      Float?      // Percentage
  trafficSources  String?     // JSON object with traffic source breakdown
  generatedAt     DateTime    @default(now())

  // Relationships
  article         NewsArticle @relation(fields: [articleId], references: [id], onDelete: Cascade)

  @@map("article_analytics")
}

model APIKey {
  id            String        @id @default(cuid())
  keyHash       String        @unique // Hashed API key
  name          String        // Human-readable name
  clientId      String?       // Optional link to client
  tier          APIAccessTier @default(FREE)
  isActive      Boolean       @default(true)
  rateLimit     Int           @default(100) // Requests per hour
  usageCount    Int           @default(0)
  lastUsedAt    DateTime?
  expiresAt     DateTime?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relationships
  client        Client?       @relation(fields: [clientId], references: [id], onDelete: SetNull)
  usage         APIUsage[]

  @@map("api_keys")
}

model APIUsage {
  id            String   @id @default(cuid())
  apiKeyId      String
  endpoint      String
  method        String
  statusCode    Int
  responseTime  Int      // Milliseconds
  ipAddress     String?
  userAgent     String?
  requestedAt   DateTime @default(now())

  // Relationships
  apiKey        APIKey   @relation(fields: [apiKeyId], references: [id], onDelete: Cascade)

  @@map("api_usage")
}

model ConsumerPulseUser {
  id            String   @id @default(cuid())
  email         String   @unique
  firstName     String?
  lastName      String?
  company       String?
  industry      String?
  isVerified    Boolean  @default(false)
  preferences   String?  // JSON object for user preferences
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@map("consumer_pulse_users")
}
