const express = require('express');
const router = express.Router();
const ConsumerPulseService = require('../services/consumerPulseService');
const AuthService = require('../services/authService');
const NewsScrapingService = require('../services/newsScrapingService');

// Middleware to get client IP
const getClientIP = (req) => {
  return req.headers['x-forwarded-for'] || 
         req.connection.remoteAddress || 
         req.socket.remoteAddress ||
         (req.connection.socket ? req.connection.socket.remoteAddress : null);
};

// Survey Routes
router.post('/surveys', AuthService.requireAuth, async (req, res) => {
  try {
    const surveyData = {
      ...req.body,
      createdBy: req.admin.id
    };

    const result = await ConsumerPulseService.createSurvey(surveyData);
    res.status(201).json(result);
  } catch (error) {
    console.error('Error creating survey:', error);
    res.status(500).json({ 
      error: 'Failed to create survey',
      details: error.message 
    });
  }
});

router.get('/surveys', async (req, res) => {
  try {
    const { page = 1, limit = 10, status, createdBy } = req.query;
    
    const filters = {};
    if (status) filters.status = status;
    if (createdBy) filters.createdBy = createdBy;

    const result = await ConsumerPulseService.getSurveys(
      parseInt(page),
      parseInt(limit),
      filters
    );

    res.json({
      success: true,
      ...result
    });
  } catch (error) {
    console.error('Error fetching surveys:', error);
    res.status(500).json({ 
      error: 'Failed to fetch surveys',
      details: error.message 
    });
  }
});

router.patch('/surveys/:id/status', AuthService.requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    if (!status) {
      return res.status(400).json({ error: 'Status is required' });
    }

    const result = await ConsumerPulseService.updateSurveyStatus(id, status);
    res.json(result);
  } catch (error) {
    console.error('Error updating survey status:', error);
    res.status(500).json({ 
      error: 'Failed to update survey status',
      details: error.message 
    });
  }
});

// Survey Response Routes
router.post('/surveys/:id/responses', async (req, res) => {
  try {
    const { id } = req.params;
    const responseData = {
      surveyId: id,
      responses: req.body.responses,
      respondentId: req.body.respondentId,
      ipAddress: getClientIP(req),
      userAgent: req.headers['user-agent'],
      location: req.body.location
    };

    const result = await ConsumerPulseService.submitSurveyResponse(responseData);
    res.status(201).json(result);
  } catch (error) {
    console.error('Error submitting survey response:', error);
    res.status(500).json({ 
      error: 'Failed to submit survey response',
      details: error.message 
    });
  }
});

// Poll Routes
router.post('/polls', AuthService.requireAuth, async (req, res) => {
  try {
    const pollData = {
      ...req.body,
      createdBy: req.admin.id
    };

    const result = await ConsumerPulseService.createPoll(pollData);
    res.status(201).json(result);
  } catch (error) {
    console.error('Error creating poll:', error);
    res.status(500).json({ 
      error: 'Failed to create poll',
      details: error.message 
    });
  }
});

router.get('/polls', async (req, res) => {
  try {
    const { page = 1, limit = 10, status } = req.query;
    
    const filters = {};
    if (status) filters.status = status;

    const result = await ConsumerPulseService.getPolls(
      parseInt(page),
      parseInt(limit),
      filters
    );

    res.json({
      success: true,
      ...result
    });
  } catch (error) {
    console.error('Error fetching polls:', error);
    res.status(500).json({ 
      error: 'Failed to fetch polls',
      details: error.message 
    });
  }
});

router.post('/polls/:id/vote', async (req, res) => {
  try {
    const { id } = req.params;
    const voteData = {
      pollId: id,
      optionIndex: req.body.optionIndex,
      voterId: req.body.voterId,
      ipAddress: getClientIP(req),
      userAgent: req.headers['user-agent']
    };

    const result = await ConsumerPulseService.submitPollVote(voteData);
    res.status(201).json(result);
  } catch (error) {
    console.error('Error submitting poll vote:', error);
    res.status(400).json({ 
      error: 'Failed to submit vote',
      details: error.message 
    });
  }
});

// News Article Routes
router.post('/articles', AuthService.requireAuth, async (req, res) => {
  try {
    const result = await ConsumerPulseService.createArticle(req.body);
    res.status(201).json(result);
  } catch (error) {
    console.error('Error creating article:', error);
    res.status(500).json({ 
      error: 'Failed to create article',
      details: error.message 
    });
  }
});

router.get('/articles', async (req, res) => {
  try {
    const { page = 1, limit = 10, status, category, sentiment } = req.query;
    
    const filters = {};
    if (status) filters.status = status;
    if (category) filters.category = category;
    if (sentiment) filters.sentiment = sentiment;

    const result = await ConsumerPulseService.getArticles(
      parseInt(page),
      parseInt(limit),
      filters
    );

    res.json({
      success: true,
      ...result
    });
  } catch (error) {
    console.error('Error fetching articles:', error);
    res.status(500).json({ 
      error: 'Failed to fetch articles',
      details: error.message 
    });
  }
});

// Article Management Routes (Admin only)
router.patch('/articles/:id/status', AuthService.requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const result = await ConsumerPulseService.updateArticleStatus(id, status);
    res.json(result);
  } catch (error) {
    console.error('Error updating article status:', error);
    res.status(500).json({
      error: 'Failed to update article status',
      details: error.message
    });
  }
});

// API Key Management Routes
router.post('/api-keys', AuthService.requireAuth, async (req, res) => {
  try {
    const result = await ConsumerPulseService.generateAPIKey(req.body);
    res.status(201).json(result);
  } catch (error) {
    console.error('Error generating API key:', error);
    res.status(500).json({
      error: 'Failed to generate API key',
      details: error.message
    });
  }
});

// Public API Routes (require API key)
router.use('/api', async (req, res, next) => {
  try {
    const apiKey = req.headers['x-api-key'];
    if (!apiKey) {
      return res.status(401).json({ error: 'API key required' });
    }

    const result = await ConsumerPulseService.validateAPIKey(apiKey);
    req.apiKeyRecord = result.keyRecord;
    next();
  } catch (error) {
    return res.status(401).json({ error: error.message });
  }
});

router.get('/api/surveys', async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const result = await ConsumerPulseService.getSurveys(
      parseInt(page),
      parseInt(limit),
      { status: 'ACTIVE' }
    );

    res.json({
      success: true,
      ...result
    });
  } catch (error) {
    res.status(500).json({ 
      error: 'Failed to fetch surveys',
      details: error.message 
    });
  }
});

router.get('/api/polls', async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const result = await ConsumerPulseService.getPolls(
      parseInt(page),
      parseInt(limit),
      { status: 'ACTIVE' }
    );

    res.json({
      success: true,
      ...result
    });
  } catch (error) {
    res.status(500).json({ 
      error: 'Failed to fetch polls',
      details: error.message 
    });
  }
});

router.get('/api/articles', async (req, res) => {
  try {
    const { page = 1, limit = 10, category, sentiment } = req.query;
    
    const filters = { status: 'PUBLISHED' };
    if (category) filters.category = category;
    if (sentiment) filters.sentiment = sentiment;

    const result = await ConsumerPulseService.getArticles(
      parseInt(page),
      parseInt(limit),
      filters
    );

    res.json({
      success: true,
      ...result
    });
  } catch (error) {
    res.status(500).json({ 
      error: 'Failed to fetch articles',
      details: error.message 
    });
  }
});

// News scraping routes (admin only)
router.post('/scrape-news', AuthService.requireAuth, async (req, res) => {
  try {
    const newsService = new NewsScrapingService();
    const result = await newsService.scrapeAndGenerateContent();

    res.json({
      success: true,
      message: 'News scraping completed',
      ...result
    });
  } catch (error) {
    console.error('Error in news scraping:', error);
    res.status(500).json({
      error: 'Failed to scrape news',
      details: error.message
    });
  }
});

router.post('/start-auto-scraping', AuthService.requireAuth, async (req, res) => {
  try {
    const { intervalHours = 6 } = req.body;
    const newsService = new NewsScrapingService();
    newsService.startAutomaticScraping(intervalHours);

    res.json({
      success: true,
      message: `Automatic news scraping started with ${intervalHours} hour intervals`
    });
  } catch (error) {
    console.error('Error starting auto scraping:', error);
    res.status(500).json({
      error: 'Failed to start automatic scraping',
      details: error.message
    });
  }
});

module.exports = router;
