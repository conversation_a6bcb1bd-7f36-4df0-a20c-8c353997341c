const { PrismaClient } = require('@prisma/client');
const crypto = require('crypto');
const AIService = require('./aiService');

const prisma = new PrismaClient();
const aiService = new AIService();

class ConsumerPulseService {
  // Survey Management
  static async createSurvey(surveyData) {
    try {
      const survey = await prisma.survey.create({
        data: {
          title: surveyData.title,
          description: surveyData.description,
          targetAudience: surveyData.targetAudience,
          questions: JSON.stringify(surveyData.questions),
          settings: JSON.stringify(surveyData.settings || {}),
          createdBy: surveyData.createdBy,
          startDate: surveyData.startDate ? new Date(surveyData.startDate) : null,
          endDate: surveyData.endDate ? new Date(surveyData.endDate) : null,
          status: surveyData.status || 'DRAFT'
        }
      });

      return { success: true, survey };
    } catch (error) {
      throw new Error(`Failed to create survey: ${error.message}`);
    }
  }

  static async getSurveys(page = 1, limit = 10, filters = {}) {
    try {
      const skip = (page - 1) * limit;
      const where = {};

      if (filters.status) where.status = filters.status;
      if (filters.createdBy) where.createdBy = filters.createdBy;

      const [surveys, total] = await Promise.all([
        prisma.survey.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
          include: {
            responses: {
              select: { id: true }
            },
            analytics: {
              orderBy: { generatedAt: 'desc' },
              take: 1
            }
          }
        }),
        prisma.survey.count({ where })
      ]);

      return {
        surveys: surveys.map(survey => ({
          ...survey,
          questions: JSON.parse(survey.questions),
          settings: JSON.parse(survey.settings || '{}'),
          responseCount: survey.responses.length,
          latestAnalytics: survey.analytics[0] || null
        })),
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      throw new Error(`Failed to fetch surveys: ${error.message}`);
    }
  }

  static async updateSurveyStatus(surveyId, status) {
    try {
      const survey = await prisma.survey.update({
        where: { id: surveyId },
        data: { status }
      });

      return { success: true, survey };
    } catch (error) {
      throw new Error(`Failed to update survey status: ${error.message}`);
    }
  }

  // Survey Response Management
  static async submitSurveyResponse(responseData) {
    try {
      const response = await prisma.surveyResponse.create({
        data: {
          surveyId: responseData.surveyId,
          respondentId: responseData.respondentId || null,
          responses: JSON.stringify(responseData.responses),
          ipAddress: responseData.ipAddress,
          userAgent: responseData.userAgent,
          location: responseData.location
        }
      });

      // Update survey analytics
      await this.updateSurveyAnalytics(responseData.surveyId);

      return { success: true, response };
    } catch (error) {
      throw new Error(`Failed to submit survey response: ${error.message}`);
    }
  }

  // Poll Management
  static async createPoll(pollData) {
    try {
      const poll = await prisma.poll.create({
        data: {
          title: pollData.title,
          question: pollData.question,
          options: JSON.stringify(pollData.options),
          allowMultiple: pollData.allowMultiple || false,
          endDate: pollData.endDate ? new Date(pollData.endDate) : null,
          createdBy: pollData.createdBy,
          status: pollData.status || 'ACTIVE'
        }
      });

      return { success: true, poll };
    } catch (error) {
      throw new Error(`Failed to create poll: ${error.message}`);
    }
  }

  static async getPolls(page = 1, limit = 10, filters = {}) {
    try {
      const skip = (page - 1) * limit;
      const where = {};

      if (filters.status) where.status = filters.status;

      const [polls, total] = await Promise.all([
        prisma.poll.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
          include: {
            votes: {
              select: { optionIndex: true }
            }
          }
        }),
        prisma.poll.count({ where })
      ]);

      return {
        polls: polls.map(poll => {
          const options = JSON.parse(poll.options);
          const voteCounts = options.map((_, index) => 
            poll.votes.filter(vote => vote.optionIndex === index).length
          );
          const totalVotes = poll.votes.length;

          return {
            ...poll,
            options,
            voteCounts,
            totalVotes,
            votes: undefined // Remove detailed votes from response
          };
        }),
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      throw new Error(`Failed to fetch polls: ${error.message}`);
    }
  }

  static async submitPollVote(voteData) {
    try {
      // Check if IP already voted (if not allowing multiple votes)
      const existingVote = await prisma.pollVote.findUnique({
        where: {
          pollId_ipAddress: {
            pollId: voteData.pollId,
            ipAddress: voteData.ipAddress
          }
        }
      });

      if (existingVote) {
        throw new Error('You have already voted in this poll');
      }

      const vote = await prisma.pollVote.create({
        data: {
          pollId: voteData.pollId,
          optionIndex: voteData.optionIndex,
          voterId: voteData.voterId || null,
          ipAddress: voteData.ipAddress,
          userAgent: voteData.userAgent
        }
      });

      return { success: true, vote };
    } catch (error) {
      throw new Error(`Failed to submit vote: ${error.message}`);
    }
  }

  // News Article Management
  static async createArticle(articleData) {
    try {
      const article = await prisma.newsArticle.create({
        data: {
          title: articleData.title,
          content: articleData.content,
          summary: articleData.summary,
          author: articleData.author,
          sourceUrl: articleData.sourceUrl,
          imageUrl: articleData.imageUrl,
          keywords: JSON.stringify(articleData.keywords || []),
          category: articleData.category,
          status: articleData.status || 'DRAFT',
          sentiment: articleData.sentiment,
          sentimentScore: articleData.sentimentScore,
          publishedAt: articleData.status === 'PUBLISHED' ? new Date() : null,
          scrapedAt: articleData.scrapedAt ? new Date(articleData.scrapedAt) : null
        }
      });

      return { success: true, article };
    } catch (error) {
      throw new Error(`Failed to create article: ${error.message}`);
    }
  }

  static async getArticles(page = 1, limit = 10, filters = {}) {
    try {
      const skip = (page - 1) * limit;
      const where = {};

      if (filters.status) where.status = filters.status;
      if (filters.category) where.category = filters.category;
      if (filters.sentiment) where.sentiment = filters.sentiment;

      const [articles, total] = await Promise.all([
        prisma.newsArticle.findMany({
          where,
          skip,
          take: limit,
          orderBy: { publishedAt: 'desc' },
          include: {
            analytics: {
              orderBy: { generatedAt: 'desc' },
              take: 1
            }
          }
        }),
        prisma.newsArticle.count({ where })
      ]);

      return {
        articles: articles.map(article => ({
          ...article,
          keywords: JSON.parse(article.keywords),
          latestAnalytics: article.analytics[0] || null
        })),
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      throw new Error(`Failed to fetch articles: ${error.message}`);
    }
  }

  static async updateArticleStatus(articleId, status) {
    try {
      const article = await prisma.newsArticle.update({
        where: { id: articleId },
        data: {
          status,
          publishedAt: status === 'PUBLISHED' ? new Date() : null
        }
      });

      return { success: true, article };
    } catch (error) {
      throw new Error(`Failed to update article status: ${error.message}`);
    }
  }

  // Analytics Methods
  static async updateSurveyAnalytics(surveyId) {
    try {
      const survey = await prisma.survey.findUnique({
        where: { id: surveyId },
        include: {
          responses: true
        }
      });

      if (!survey) {
        throw new Error('Survey not found');
      }

      const totalResponses = survey.responses.length;
      if (totalResponses === 0) return;

      // Generate AI-powered insights
      const insights = await aiService.generateSurveyInsights(survey, survey.responses);

      // Analyze sentiment of text responses
      const textResponses = survey.responses
        .map(r => JSON.parse(r.responses))
        .filter(responses => Object.values(responses).some(val => typeof val === 'string' && val.length > 10))
        .flatMap(responses => Object.values(responses).filter(val => typeof val === 'string' && val.length > 10));

      let averageSentiment = 0;
      if (textResponses.length > 0) {
        const sentiments = await aiService.batchSentimentAnalysis(textResponses);
        averageSentiment = sentiments.reduce((sum, s) => sum + s.score, 0) / sentiments.length;
      }

      // Calculate completion rate (simplified)
      const completionRate = 100; // All stored responses are complete

      const analytics = await prisma.surveyAnalytics.create({
        data: {
          surveyId,
          totalResponses,
          completionRate,
          sentimentScore: averageSentiment,
          insights: JSON.stringify(insights),
          demographics: JSON.stringify({
            total: totalResponses,
            textResponseCount: textResponses.length
          })
        }
      });

      return { success: true, analytics };
    } catch (error) {
      throw new Error(`Failed to update survey analytics: ${error.message}`);
    }
  }

  // API Key Management
  static async generateAPIKey(keyData) {
    try {
      const apiKey = crypto.randomBytes(32).toString('hex');
      const keyHash = crypto.createHash('sha256').update(apiKey).digest('hex');

      const apiKeyRecord = await prisma.aPIKey.create({
        data: {
          keyHash,
          name: keyData.name,
          clientId: keyData.clientId || null,
          tier: keyData.tier || 'FREE',
          rateLimit: keyData.rateLimit || 100,
          expiresAt: keyData.expiresAt ? new Date(keyData.expiresAt) : null
        }
      });

      return { 
        success: true, 
        apiKey: apiKey, // Return the actual key only once
        keyRecord: apiKeyRecord 
      };
    } catch (error) {
      throw new Error(`Failed to generate API key: ${error.message}`);
    }
  }

  static async validateAPIKey(apiKey) {
    try {
      const keyHash = crypto.createHash('sha256').update(apiKey).digest('hex');
      
      const keyRecord = await prisma.aPIKey.findUnique({
        where: { keyHash },
        include: { client: true }
      });

      if (!keyRecord || !keyRecord.isActive) {
        throw new Error('Invalid or inactive API key');
      }

      if (keyRecord.expiresAt && keyRecord.expiresAt < new Date()) {
        throw new Error('API key has expired');
      }

      // Update last used timestamp
      await prisma.aPIKey.update({
        where: { id: keyRecord.id },
        data: { 
          lastUsedAt: new Date(),
          usageCount: { increment: 1 }
        }
      });

      return { success: true, keyRecord };
    } catch (error) {
      throw new Error(`API key validation failed: ${error.message}`);
    }
  }
}

module.exports = ConsumerPulseService;
