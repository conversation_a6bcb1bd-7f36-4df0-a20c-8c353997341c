const axios = require('axios');
const cheerio = require('cheerio');
const ConsumerPulseService = require('./consumerPulseService');
const AIService = require('./aiService');

class NewsScrapingService {
  constructor() {
    this.aiService = new AIService();
    // News sources configuration - 5 reliable news sources for real-time data
    this.newsSources = [
      {
        name: 'BBC News',
        url: 'https://www.bbc.com/news',
        selectors: {
          articles: '[data-testid="card-headline"], .gs-c-promo',
          title: 'h3, .gs-c-promo-heading__title',
          link: 'a',
          summary: '.gs-c-promo-summary, p'
        },
        maxArticles: 15
      },
      {
        name: 'CNN',
        url: 'https://lite.cnn.com/',
        selectors: {
          articles: 'li',
          title: 'a',
          link: 'a',
          summary: 'span'
        },
        maxArticles: 20
      },
      {
        name: 'Reuters',
        url: 'https://www.reuters.com/world/',
        selectors: {
          articles: '[data-testid="Body"] article, .story-card',
          title: 'h3 a, .story-card__headline a',
          link: 'h3 a, .story-card__headline a',
          summary: 'p, .story-card__summary'
        },
        maxArticles: 15
      },
      {
        name: 'Associated Press',
        url: 'https://apnews.com/',
        selectors: {
          articles: '.PagePromo, .CardHeadline',
          title: '.PagePromoContentIcons-text, .CardHeadline-text',
          link: 'a',
          summary: '.PagePromo-description, p'
        },
        maxArticles: 12
      },
      {
        name: 'NPR News',
        url: 'https://text.npr.org/',
        selectors: {
          articles: 'article, .item',
          title: 'h2 a, h3 a, .title a',
          link: 'h2 a, h3 a, .title a',
          summary: 'p, .teaser'
        },
        maxArticles: 10
      }
    ];

    // Enhanced keywords for traffic direction and SEO
    this.targetKeywords = [
      // Core market research terms
      'market research', 'consumer behavior', 'business analytics',
      'survey data', 'polling results', 'consumer sentiment',
      'market trends', 'business intelligence', 'data analysis',
      'consumer insights', 'market analysis', 'business surveys',

      // Industry-specific terms
      'customer feedback', 'brand perception', 'market segmentation',
      'competitive analysis', 'consumer preferences', 'purchase behavior',
      'market share analysis', 'customer satisfaction', 'brand loyalty',

      // Technology and AI terms
      'AI sentiment analysis', 'automated surveys', 'real-time polling',
      'predictive analytics', 'machine learning insights', 'data visualization',
      'consumer pulse', 'market intelligence platform', 'survey automation',

      // Business decision terms
      'strategic planning', 'market opportunity', 'consumer demand',
      'business growth', 'market validation', 'product development',
      'customer journey', 'market positioning', 'consumer research'
    ];

    // SEO-optimized content templates
    this.contentTemplates = {
      intro: [
        "According to recent market research data,",
        "Consumer sentiment analysis reveals that",
        "Latest polling results indicate",
        "Market intelligence suggests",
        "Business analytics show"
      ],
      insights: [
        "This trend aligns with consumer behavior patterns observed in our surveys.",
        "Our automated sentiment analysis indicates similar market movements.",
        "Real-time polling data supports these findings.",
        "Consumer pulse metrics confirm this market direction.",
        "Business intelligence platforms are tracking similar patterns."
      ],
      cta: [
        "Stay ahead of market trends with Consumer Pulse's real-time analytics.",
        "Get deeper insights with our AI-powered market research platform.",
        "Track consumer sentiment with automated polling and survey tools.",
        "Discover what drives consumer behavior with our analytics dashboard.",
        "Make data-driven decisions with Consumer Pulse market intelligence."
      ]
    };
  }

  // Scrape news from configured sources
  async scrapeNews() {
    const scrapedArticles = [];
    const startTime = Date.now();

    console.log(`Starting news scraping from ${this.newsSources.length} sources...`);

    // Process sources in parallel for faster scraping
    const scrapingPromises = this.newsSources.map(async (source) => {
      try {
        console.log(`Scraping from ${source.name}...`);
        const articles = await this.scrapeFromSource(source);
        console.log(`✅ ${source.name}: Found ${articles.length} articles`);
        return articles;
      } catch (error) {
        console.error(`❌ ${source.name}: ${error.message}`);
        return [];
      }
    });

    // Wait for all scraping to complete
    const results = await Promise.allSettled(scrapingPromises);

    // Collect all successful results
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        scrapedArticles.push(...result.value);
      } else {
        console.error(`Failed to scrape ${this.newsSources[index].name}:`, result.reason);
      }
    });

    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);

    console.log(`📊 Scraping completed in ${duration}s. Total articles found: ${scrapedArticles.length}`);

    // Remove duplicates based on title similarity
    const uniqueArticles = this.removeDuplicates(scrapedArticles);
    console.log(`📝 After deduplication: ${uniqueArticles.length} unique articles`);

    return uniqueArticles;
  }

  // Remove duplicate articles
  removeDuplicates(articles) {
    const seen = new Set();
    return articles.filter(article => {
      const key = article.title.toLowerCase().replace(/[^\w\s]/g, '').trim();
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  async scrapeFromSource(source) {
    try {
      const response = await axios.get(source.url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
          'Sec-Fetch-Dest': 'document',
          'Sec-Fetch-Mode': 'navigate',
          'Sec-Fetch-Site': 'none'
        },
        timeout: 15000,
        maxRedirects: 5
      });

      const $ = cheerio.load(response.data);
      const articles = [];
      const maxArticles = source.maxArticles || 15;
      let foundArticles = 0;

      // Try multiple selector strategies for better coverage
      const selectorGroups = source.selectors.articles.split(',').map(s => s.trim());

      for (const articleSelector of selectorGroups) {
        if (foundArticles >= maxArticles) break;

        $(articleSelector).each((index, element) => {
          if (foundArticles >= maxArticles) return false;

          try {
            const $article = $(element);

            // Try multiple title selectors
            let title = '';
            const titleSelectors = source.selectors.title.split(',').map(s => s.trim());
            for (const titleSelector of titleSelectors) {
              title = $article.find(titleSelector).first().text().trim();
              if (title && title.length > 5) break;
            }

            // Try multiple link selectors
            let link = '';
            const linkSelectors = source.selectors.link.split(',').map(s => s.trim());
            for (const linkSelector of linkSelectors) {
              const linkElement = $article.find(linkSelector).first();
              link = linkElement.attr('href');
              if (link) break;
            }

            // Try multiple summary selectors
            let summary = '';
            const summarySelectors = source.selectors.summary.split(',').map(s => s.trim());
            for (const summarySelector of summarySelectors) {
              summary = $article.find(summarySelector).first().text().trim();
              if (summary && summary.length > 10 && summary !== title) break;
            }

            // Validate and process the article
            if (title && title.length > 5 && title.length < 200) {
              // Ensure absolute URL
              let absoluteLink = this.makeAbsoluteUrl(link, source.url);

              // Create meaningful content
              const content = this.generateArticleContent(title, summary, source.name);

              // Check for duplicates
              const isDuplicate = articles.some(existing =>
                existing.title.toLowerCase() === title.toLowerCase() ||
                existing.sourceUrl === absoluteLink
              );

              if (!isDuplicate) {
                articles.push({
                  title: title.substring(0, 200),
                  content,
                  summary: summary.substring(0, 300) || title,
                  sourceUrl: absoluteLink,
                  source: source.name,
                  scrapedAt: new Date()
                });
                foundArticles++;
              }
            }
          } catch (error) {
            console.error(`Error parsing article from ${source.name}:`, error.message);
          }
        });
      }

      console.log(`Successfully scraped ${articles.length} articles from ${source.name}`);
      return articles;
    } catch (error) {
      console.error(`Failed to scrape ${source.name}:`, error.message);
      return []; // Return empty array instead of throwing to continue with other sources
    }
  }

  // Helper method to make absolute URLs
  makeAbsoluteUrl(link, baseUrl) {
    if (!link) return baseUrl;

    try {
      if (link.startsWith('http')) {
        return link;
      } else if (link.startsWith('//')) {
        const protocol = new URL(baseUrl).protocol;
        return `${protocol}${link}`;
      } else if (link.startsWith('/')) {
        const base = new URL(baseUrl);
        return `${base.protocol}//${base.host}${link}`;
      } else {
        return new URL(link, baseUrl).href;
      }
    } catch (error) {
      console.error('Error creating absolute URL:', error.message);
      return baseUrl;
    }
  }

  // Generate meaningful content for articles
  generateArticleContent(title, summary, sourceName) {
    let baseContent = title;

    if (summary && summary !== title && summary.length > 10) {
      baseContent = `${title}\n\n${summary}`;
    } else {
      baseContent = `${title}\n\nThis breaking news story from ${sourceName} covers important developments that have significant implications for businesses and consumers.`;
    }

    // Add market research context
    const marketContext = `

## Market Research Analysis

This development presents several key insights for market researchers and business analysts:

**Consumer Behavior Impact:**
- Potential shifts in consumer sentiment and purchasing patterns
- Changes in brand perception and market positioning
- Opportunities for targeted market research and consumer surveys

**Business Intelligence Opportunities:**
- Real-time polling data can reveal consumer reactions
- Sentiment analysis provides immediate market feedback
- Strategic planning should incorporate these market dynamics

**Key Research Indicators:**
• Consumer confidence levels in affected sectors
• Brand sentiment analysis and reputation monitoring
• Market opportunity assessment and competitive analysis
• Purchasing behavior pattern changes

For comprehensive market research insights and real-time consumer sentiment analysis, businesses can leverage our Consumer Pulse platform to understand how these developments impact their target markets.`;

    return baseContent + marketContext;
  }

  // Generate AI-powered content based on scraped news
  async generateContentFromNews(scrapedArticles) {
    const generatedArticles = [];

    for (const article of scrapedArticles) {
      try {
        // Generate enhanced content with AI
        const enhancedArticle = await this.aiService.enhanceArticleContent(article);

        // Add SEO keywords
        const keywordOptimizedArticle = this.addSEOKeywords(enhancedArticle);

        // Analyze sentiment using AI
        const sentimentAnalysis = await this.aiService.analyzeSentiment(keywordOptimizedArticle.content);
        
        const finalArticle = {
          ...keywordOptimizedArticle,
          sentiment: sentimentAnalysis.sentiment,
          sentimentScore: sentimentAnalysis.score,
          status: 'DRAFT',
          category: this.categorizeArticle(keywordOptimizedArticle.title)
        };

        generatedArticles.push(finalArticle);
      } catch (error) {
        console.error('Error generating content for article:', error.message);
      }
    }

    return generatedArticles;
  }

  // Enhance article with market research perspective
  async enhanceArticleWithMarketResearchAngle(article) {
    // This is a simplified version. In production, you'd use AI APIs like OpenAI
    const marketResearchIntro = `
      Recent market research and consumer sentiment analysis reveals important insights about ${article.title.toLowerCase()}.
      This development has significant implications for consumer behavior and market dynamics.
    `;

    const marketResearchConclusion = `
      Consumer Pulse Analysis: This trend reflects broader market sentiments that businesses should monitor closely.
      Our polling data suggests that consumer confidence in this sector may be shifting.
      For detailed market research and consumer insights, businesses can access our comprehensive analytics platform.
    `;

    const enhancedContent = `
      ${marketResearchIntro}
      
      ${article.summary}
      
      Market Research Implications:
      The developments outlined in this story highlight the importance of real-time consumer sentiment tracking.
      Businesses operating in related sectors should consider conducting targeted surveys to understand
      how these changes might affect their customer base.
      
      ${marketResearchConclusion}
    `;

    return {
      ...article,
      content: enhancedContent,
      author: 'Consumer Pulse Research Team'
    };
  }

  // Enhanced SEO keyword optimization with traffic direction strategy
  addSEOKeywords(article) {
    const contentLower = (article.title + ' ' + article.content).toLowerCase();

    // Find relevant keywords based on content analysis
    const relevantKeywords = this.targetKeywords.filter(keyword => {
      const keywordParts = keyword.toLowerCase().split(' ');
      return keywordParts.some(part => contentLower.includes(part));
    });

    // Ensure minimum keywords for SEO
    if (relevantKeywords.length < 3) {
      const defaultKeywords = [
        'market research', 'consumer insights', 'business analytics',
        'consumer sentiment', 'market trends', 'data analysis'
      ];
      relevantKeywords.push(...defaultKeywords.slice(0, 3 - relevantKeywords.length));
    }

    // Select random content template elements
    const randomIntro = this.contentTemplates.intro[Math.floor(Math.random() * this.contentTemplates.intro.length)];
    const randomInsight = this.contentTemplates.insights[Math.floor(Math.random() * this.contentTemplates.insights.length)];
    const randomCTA = this.contentTemplates.cta[Math.floor(Math.random() * this.contentTemplates.cta.length)];

    // Create SEO-optimized content addition
    const seoEnhancement = `

## Market Research Insights

${randomIntro} this development has significant implications for ${relevantKeywords[0]} and overall market dynamics.

${randomInsight}

### Key Takeaways for Business Leaders:
- Monitor ${relevantKeywords[1]} trends closely
- Consider ${relevantKeywords[2]} in strategic planning
- Leverage real-time data for competitive advantage

---

**About Consumer Pulse:** ${randomCTA}

*Keywords: ${relevantKeywords.join(', ')}*
    `;

    return {
      ...article,
      content: article.content + seoEnhancement,
      keywords: relevantKeywords,
      seoOptimized: true,
      trafficKeywords: relevantKeywords.slice(0, 5) // Top 5 for meta tags
    };
  }

  // Simple sentiment analysis (in production, use AI services)
  analyzeSentiment(content) {
    const positiveWords = ['growth', 'increase', 'positive', 'success', 'gain', 'rise', 'improve', 'strong'];
    const negativeWords = ['decline', 'decrease', 'negative', 'loss', 'fall', 'drop', 'weak', 'concern'];

    const words = content.toLowerCase().split(/\s+/);
    let positiveCount = 0;
    let negativeCount = 0;

    words.forEach(word => {
      if (positiveWords.some(pw => word.includes(pw))) positiveCount++;
      if (negativeWords.some(nw => word.includes(nw))) negativeCount++;
    });

    const totalSentimentWords = positiveCount + negativeCount;
    if (totalSentimentWords === 0) {
      return { sentiment: 'NEUTRAL', score: 0 };
    }

    const score = (positiveCount - negativeCount) / totalSentimentWords;
    
    let sentiment;
    if (score > 0.2) sentiment = 'POSITIVE';
    else if (score < -0.2) sentiment = 'NEGATIVE';
    else sentiment = 'NEUTRAL';

    return { sentiment, score };
  }

  // Categorize articles
  categorizeArticle(title) {
    const categories = {
      'Technology': ['tech', 'digital', 'ai', 'software', 'data'],
      'Finance': ['bank', 'finance', 'money', 'investment', 'market'],
      'Business': ['business', 'company', 'corporate', 'industry'],
      'Economics': ['economy', 'economic', 'gdp', 'inflation', 'trade'],
      'Consumer': ['consumer', 'retail', 'shopping', 'customer']
    };

    const titleLower = title.toLowerCase();
    
    for (const [category, keywords] of Object.entries(categories)) {
      if (keywords.some(keyword => titleLower.includes(keyword))) {
        return category;
      }
    }

    return 'General';
  }

  // Main method to scrape and generate content
  async scrapeAndGenerateContent() {
    try {
      console.log('Starting news scraping and content generation...');
      
      // Scrape news
      const scrapedArticles = await this.scrapeNews();
      console.log(`Scraped ${scrapedArticles.length} articles`);

      if (scrapedArticles.length === 0) {
        console.log('No articles scraped');
        return { success: false, message: 'No articles found' };
      }

      // Generate enhanced content
      const generatedArticles = await this.generateContentFromNews(scrapedArticles);
      console.log(`Generated ${generatedArticles.length} enhanced articles`);

      // Save to database
      const savedArticles = [];
      for (const article of generatedArticles) {
        try {
          const result = await ConsumerPulseService.createArticle(article);
          if (result.success) {
            savedArticles.push(result.article);
          }
        } catch (error) {
          console.error('Error saving article:', error.message);
        }
      }

      console.log(`Saved ${savedArticles.length} articles to database`);

      return {
        success: true,
        scraped: scrapedArticles.length,
        generated: generatedArticles.length,
        saved: savedArticles.length,
        articles: savedArticles
      };
    } catch (error) {
      console.error('Error in scrapeAndGenerateContent:', error);
      return { success: false, error: error.message };
    }
  }

  // Schedule automatic content generation
  startAutomaticScraping(intervalHours = 6) {
    console.log(`Starting automatic news scraping every ${intervalHours} hours`);
    
    // Run immediately
    this.scrapeAndGenerateContent();
    
    // Schedule recurring runs
    setInterval(() => {
      this.scrapeAndGenerateContent();
    }, intervalHours * 60 * 60 * 1000);
  }
}

module.exports = NewsScrapingService;
