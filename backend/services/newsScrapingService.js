const axios = require('axios');
const cheerio = require('cheerio');
const ConsumerPulseService = require('./consumerPulseService');
const AIService = require('./aiService');

class NewsScrapingService {
  constructor() {
    this.aiService = new AIService();
    // News sources configuration
    this.newsSources = [
      {
        name: 'Reuters Business',
        url: 'https://www.reuters.com/business/',
        selectors: {
          articles: 'article',
          title: 'h3 a, h2 a',
          link: 'h3 a, h2 a',
          summary: 'p'
        }
      },
      {
        name: 'BBC Business',
        url: 'https://www.bbc.com/news/business',
        selectors: {
          articles: '[data-testid="edinburgh-article"]',
          title: 'h3',
          link: 'a',
          summary: 'p'
        }
      },
      {
        name: 'CNN Business',
        url: 'https://www.cnn.com/business',
        selectors: {
          articles: '.container__item',
          title: '.container__headline-text',
          link: 'a',
          summary: '.container__description'
        }
      },
      {
        name: 'MarketWatch',
        url: 'https://www.marketwatch.com/',
        selectors: {
          articles: '.article__content',
          title: '.article__headline a',
          link: '.article__headline a',
          summary: '.article__summary'
        }
      },
      {
        name: 'Financial Times',
        url: 'https://www.ft.com/companies',
        selectors: {
          articles: '.o-teaser',
          title: '.o-teaser__heading a',
          link: '.o-teaser__heading a',
          summary: '.o-teaser__standfirst'
        }
      },
      {
        name: 'Bloomberg',
        url: 'https://www.bloomberg.com/markets',
        selectors: {
          articles: '[data-module="Story"]',
          title: 'h3 a, h2 a',
          link: 'h3 a, h2 a',
          summary: 'p'
        }
      },
      {
        name: 'Yahoo Finance',
        url: 'https://finance.yahoo.com/news/',
        selectors: {
          articles: '[data-test-locator="mega"]',
          title: 'h3 a',
          link: 'h3 a',
          summary: 'p'
        }
      },
      {
        name: 'TechCrunch',
        url: 'https://techcrunch.com/',
        selectors: {
          articles: '.post-block',
          title: '.post-block__title__link',
          link: '.post-block__title__link',
          summary: '.post-block__content'
        }
      }
    ];

    // Enhanced keywords for traffic direction and SEO
    this.targetKeywords = [
      // Core market research terms
      'market research', 'consumer behavior', 'business analytics',
      'survey data', 'polling results', 'consumer sentiment',
      'market trends', 'business intelligence', 'data analysis',
      'consumer insights', 'market analysis', 'business surveys',

      // Industry-specific terms
      'customer feedback', 'brand perception', 'market segmentation',
      'competitive analysis', 'consumer preferences', 'purchase behavior',
      'market share analysis', 'customer satisfaction', 'brand loyalty',

      // Technology and AI terms
      'AI sentiment analysis', 'automated surveys', 'real-time polling',
      'predictive analytics', 'machine learning insights', 'data visualization',
      'consumer pulse', 'market intelligence platform', 'survey automation',

      // Business decision terms
      'strategic planning', 'market opportunity', 'consumer demand',
      'business growth', 'market validation', 'product development',
      'customer journey', 'market positioning', 'consumer research'
    ];

    // SEO-optimized content templates
    this.contentTemplates = {
      intro: [
        "According to recent market research data,",
        "Consumer sentiment analysis reveals that",
        "Latest polling results indicate",
        "Market intelligence suggests",
        "Business analytics show"
      ],
      insights: [
        "This trend aligns with consumer behavior patterns observed in our surveys.",
        "Our automated sentiment analysis indicates similar market movements.",
        "Real-time polling data supports these findings.",
        "Consumer pulse metrics confirm this market direction.",
        "Business intelligence platforms are tracking similar patterns."
      ],
      cta: [
        "Stay ahead of market trends with Consumer Pulse's real-time analytics.",
        "Get deeper insights with our AI-powered market research platform.",
        "Track consumer sentiment with automated polling and survey tools.",
        "Discover what drives consumer behavior with our analytics dashboard.",
        "Make data-driven decisions with Consumer Pulse market intelligence."
      ]
    };
  }

  // Scrape news from configured sources
  async scrapeNews() {
    const scrapedArticles = [];

    for (const source of this.newsSources) {
      try {
        console.log(`Scraping from ${source.name}...`);
        const articles = await this.scrapeFromSource(source);
        scrapedArticles.push(...articles);
      } catch (error) {
        console.error(`Error scraping ${source.name}:`, error.message);
      }
    }

    return scrapedArticles;
  }

  async scrapeFromSource(source) {
    try {
      const response = await axios.get(source.url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        },
        timeout: 10000
      });

      const $ = cheerio.load(response.data);
      const articles = [];

      $(source.selectors.articles).each((index, element) => {
        try {
          const $article = $(element);
          const title = $article.find(source.selectors.title).first().text().trim();
          const linkElement = $article.find(source.selectors.link).first();
          const link = linkElement.attr('href');
          const summary = $article.find(source.selectors.summary).first().text().trim();

          if (title && link) {
            // Ensure absolute URL
            const absoluteLink = link.startsWith('http') ? link : new URL(link, source.url).href;

            // Create meaningful content from title and summary
            const content = summary && summary !== title
              ? `${title}\n\n${summary}`
              : `${title}\n\nThis article discusses recent developments in ${title.toLowerCase()}. The story covers important market trends and business implications that are relevant to consumer behavior and market research analysis.`;

            articles.push({
              title,
              content, // Add actual content instead of undefined
              summary: summary || title,
              sourceUrl: absoluteLink,
              source: source.name,
              scrapedAt: new Date()
            });
          }
        } catch (error) {
          console.error('Error parsing article:', error.message);
        }
      });

      return articles.slice(0, 10); // Limit to 10 articles per source
    } catch (error) {
      throw new Error(`Failed to scrape ${source.name}: ${error.message}`);
    }
  }

  // Generate AI-powered content based on scraped news
  async generateContentFromNews(scrapedArticles) {
    const generatedArticles = [];

    for (const article of scrapedArticles) {
      try {
        // Generate enhanced content with AI
        const enhancedArticle = await this.aiService.enhanceArticleContent(article);

        // Add SEO keywords
        const keywordOptimizedArticle = this.addSEOKeywords(enhancedArticle);

        // Analyze sentiment using AI
        const sentimentAnalysis = await this.aiService.analyzeSentiment(keywordOptimizedArticle.content);
        
        const finalArticle = {
          ...keywordOptimizedArticle,
          sentiment: sentimentAnalysis.sentiment,
          sentimentScore: sentimentAnalysis.score,
          status: 'DRAFT',
          category: this.categorizeArticle(keywordOptimizedArticle.title)
        };

        generatedArticles.push(finalArticle);
      } catch (error) {
        console.error('Error generating content for article:', error.message);
      }
    }

    return generatedArticles;
  }

  // Enhance article with market research perspective
  async enhanceArticleWithMarketResearchAngle(article) {
    // This is a simplified version. In production, you'd use AI APIs like OpenAI
    const marketResearchIntro = `
      Recent market research and consumer sentiment analysis reveals important insights about ${article.title.toLowerCase()}.
      This development has significant implications for consumer behavior and market dynamics.
    `;

    const marketResearchConclusion = `
      Consumer Pulse Analysis: This trend reflects broader market sentiments that businesses should monitor closely.
      Our polling data suggests that consumer confidence in this sector may be shifting.
      For detailed market research and consumer insights, businesses can access our comprehensive analytics platform.
    `;

    const enhancedContent = `
      ${marketResearchIntro}
      
      ${article.summary}
      
      Market Research Implications:
      The developments outlined in this story highlight the importance of real-time consumer sentiment tracking.
      Businesses operating in related sectors should consider conducting targeted surveys to understand
      how these changes might affect their customer base.
      
      ${marketResearchConclusion}
    `;

    return {
      ...article,
      content: enhancedContent,
      author: 'Consumer Pulse Research Team'
    };
  }

  // Enhanced SEO keyword optimization with traffic direction strategy
  addSEOKeywords(article) {
    const contentLower = (article.title + ' ' + article.content).toLowerCase();

    // Find relevant keywords based on content analysis
    const relevantKeywords = this.targetKeywords.filter(keyword => {
      const keywordParts = keyword.toLowerCase().split(' ');
      return keywordParts.some(part => contentLower.includes(part));
    });

    // Ensure minimum keywords for SEO
    if (relevantKeywords.length < 3) {
      const defaultKeywords = [
        'market research', 'consumer insights', 'business analytics',
        'consumer sentiment', 'market trends', 'data analysis'
      ];
      relevantKeywords.push(...defaultKeywords.slice(0, 3 - relevantKeywords.length));
    }

    // Select random content template elements
    const randomIntro = this.contentTemplates.intro[Math.floor(Math.random() * this.contentTemplates.intro.length)];
    const randomInsight = this.contentTemplates.insights[Math.floor(Math.random() * this.contentTemplates.insights.length)];
    const randomCTA = this.contentTemplates.cta[Math.floor(Math.random() * this.contentTemplates.cta.length)];

    // Create SEO-optimized content addition
    const seoEnhancement = `

## Market Research Insights

${randomIntro} this development has significant implications for ${relevantKeywords[0]} and overall market dynamics.

${randomInsight}

### Key Takeaways for Business Leaders:
- Monitor ${relevantKeywords[1]} trends closely
- Consider ${relevantKeywords[2]} in strategic planning
- Leverage real-time data for competitive advantage

---

**About Consumer Pulse:** ${randomCTA}

*Keywords: ${relevantKeywords.join(', ')}*
    `;

    return {
      ...article,
      content: article.content + seoEnhancement,
      keywords: relevantKeywords,
      seoOptimized: true,
      trafficKeywords: relevantKeywords.slice(0, 5) // Top 5 for meta tags
    };
  }

  // Simple sentiment analysis (in production, use AI services)
  analyzeSentiment(content) {
    const positiveWords = ['growth', 'increase', 'positive', 'success', 'gain', 'rise', 'improve', 'strong'];
    const negativeWords = ['decline', 'decrease', 'negative', 'loss', 'fall', 'drop', 'weak', 'concern'];

    const words = content.toLowerCase().split(/\s+/);
    let positiveCount = 0;
    let negativeCount = 0;

    words.forEach(word => {
      if (positiveWords.some(pw => word.includes(pw))) positiveCount++;
      if (negativeWords.some(nw => word.includes(nw))) negativeCount++;
    });

    const totalSentimentWords = positiveCount + negativeCount;
    if (totalSentimentWords === 0) {
      return { sentiment: 'NEUTRAL', score: 0 };
    }

    const score = (positiveCount - negativeCount) / totalSentimentWords;
    
    let sentiment;
    if (score > 0.2) sentiment = 'POSITIVE';
    else if (score < -0.2) sentiment = 'NEGATIVE';
    else sentiment = 'NEUTRAL';

    return { sentiment, score };
  }

  // Categorize articles
  categorizeArticle(title) {
    const categories = {
      'Technology': ['tech', 'digital', 'ai', 'software', 'data'],
      'Finance': ['bank', 'finance', 'money', 'investment', 'market'],
      'Business': ['business', 'company', 'corporate', 'industry'],
      'Economics': ['economy', 'economic', 'gdp', 'inflation', 'trade'],
      'Consumer': ['consumer', 'retail', 'shopping', 'customer']
    };

    const titleLower = title.toLowerCase();
    
    for (const [category, keywords] of Object.entries(categories)) {
      if (keywords.some(keyword => titleLower.includes(keyword))) {
        return category;
      }
    }

    return 'General';
  }

  // Main method to scrape and generate content
  async scrapeAndGenerateContent() {
    try {
      console.log('Starting news scraping and content generation...');
      
      // Scrape news
      const scrapedArticles = await this.scrapeNews();
      console.log(`Scraped ${scrapedArticles.length} articles`);

      if (scrapedArticles.length === 0) {
        console.log('No articles scraped');
        return { success: false, message: 'No articles found' };
      }

      // Generate enhanced content
      const generatedArticles = await this.generateContentFromNews(scrapedArticles);
      console.log(`Generated ${generatedArticles.length} enhanced articles`);

      // Save to database
      const savedArticles = [];
      for (const article of generatedArticles) {
        try {
          const result = await ConsumerPulseService.createArticle(article);
          if (result.success) {
            savedArticles.push(result.article);
          }
        } catch (error) {
          console.error('Error saving article:', error.message);
        }
      }

      console.log(`Saved ${savedArticles.length} articles to database`);

      return {
        success: true,
        scraped: scrapedArticles.length,
        generated: generatedArticles.length,
        saved: savedArticles.length,
        articles: savedArticles
      };
    } catch (error) {
      console.error('Error in scrapeAndGenerateContent:', error);
      return { success: false, error: error.message };
    }
  }

  // Schedule automatic content generation
  startAutomaticScraping(intervalHours = 6) {
    console.log(`Starting automatic news scraping every ${intervalHours} hours`);
    
    // Run immediately
    this.scrapeAndGenerateContent();
    
    // Schedule recurring runs
    setInterval(() => {
      this.scrapeAndGenerateContent();
    }, intervalHours * 60 * 60 * 1000);
  }
}

module.exports = NewsScrapingService;
